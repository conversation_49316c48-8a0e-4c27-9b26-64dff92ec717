package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRequest represents a test HTTP request
type TestRequest struct {
	Method      string
	URL         string
	Body        interface{}
	Headers     map[string]string
	QueryParams map[string]string
}

// TestResponse represents a test HTTP response
type TestResponse struct {
	StatusCode int
	Body       map[string]interface{}
	Headers    http.Header
}

// MakeRequest makes an HTTP request to the test router
func MakeRequest(t *testing.T, router *gin.Engine, req TestRequest) *TestResponse {
	// Prepare request body
	var bodyReader *bytes.Reader
	if req.Body != nil {
		bodyBytes, err := json.Marshal(req.Body)
		require.NoError(t, err)
		bodyReader = bytes.NewReader(bodyBytes)
	} else {
		bodyReader = bytes.NewReader([]byte{})
	}

	// Create HTTP request
	httpReq, err := http.NewRequest(req.Method, req.URL, bodyReader)
	require.NoError(t, err)

	// Add headers
	if req.Headers != nil {
		for key, value := range req.Headers {
			httpReq.Header.Set(key, value)
		}
	}

	// Add query parameters
	if req.QueryParams != nil {
		q := httpReq.URL.Query()
		for key, value := range req.QueryParams {
			q.Add(key, value)
		}
		httpReq.URL.RawQuery = q.Encode()
	}

	// Set default content type
	if httpReq.Header.Get("Content-Type") == "" && req.Body != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// Create response recorder
	w := httptest.NewRecorder()

	// Make request
	router.ServeHTTP(w, httpReq)

	// Parse response body
	var responseBody map[string]interface{}
	if w.Body.Len() > 0 {
		err := json.Unmarshal(w.Body.Bytes(), &responseBody)
		if err != nil {
			// If JSON parsing fails, store raw body as string
			responseBody = map[string]interface{}{
				"raw": w.Body.String(),
			}
		}
	}

	return &TestResponse{
		StatusCode: w.Code,
		Body:       responseBody,
		Headers:    w.Header(),
	}
}

// AssertSuccessResponse asserts that the response is successful (2xx)
func AssertSuccessResponse(t *testing.T, resp *TestResponse) {
	assert.True(t, resp.StatusCode >= 200 && resp.StatusCode < 300,
		"Expected success status code, got %d", resp.StatusCode)
}

// AssertErrorResponse asserts that the response is an error (4xx or 5xx)
func AssertErrorResponse(t *testing.T, resp *TestResponse, expectedCode int) {
	assert.Equal(t, expectedCode, resp.StatusCode)
	assert.Contains(t, resp.Body, "error")
}

// AssertJSONResponse asserts that the response contains expected JSON fields
func AssertJSONResponse(t *testing.T, resp *TestResponse, expectedFields map[string]interface{}) {
	for key, expectedValue := range expectedFields {
		actualValue, exists := resp.Body[key]
		assert.True(t, exists, "Expected field '%s' not found in response", key)
		if exists {
			assert.Equal(t, expectedValue, actualValue, "Field '%s' has unexpected value", key)
		}
	}
}

// GenerateJWTToken generates a JWT token for testing
func GenerateJWTToken(userID uuid.UUID, email string) (string, error) {
	// Use a test secret
	secret := []byte("test-jwt-secret-key-for-testing-only")

	// Create claims
	claims := jwt.MapClaims{
		"user_id": userID.String(),
		"email":   email,
		"exp":     jwt.NewNumericDate(time.Now().Add(24 * time.Hour)), // 24 hours
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token
	tokenString, err := token.SignedString(secret)
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// WithAuth adds authentication headers to a request
func WithAuth(req TestRequest, userID uuid.UUID, email string) TestRequest {
	token, err := GenerateJWTToken(userID, email)
	if err != nil {
		panic(fmt.Sprintf("Failed to generate JWT token: %v", err))
	}

	if req.Headers == nil {
		req.Headers = make(map[string]string)
	}
	req.Headers["Authorization"] = "Bearer " + token

	return req
}

// WithAPIKey adds API key authentication to a request
func WithAPIKey(req TestRequest, apiKey string) TestRequest {
	if req.Headers == nil {
		req.Headers = make(map[string]string)
	}
	req.Headers["X-API-Key"] = apiKey

	return req
}

// CreateTestUserWithAuth creates a test user and returns auth token
func CreateTestUserWithAuth(t *testing.T, email, name string) (*models.User, string) {
	user, err := CreateTestUser(email, name)
	require.NoError(t, err)

	token, err := GenerateJWTToken(user.ID, user.Email)
	require.NoError(t, err)

	return user, token
}

// CreateTestUserWithSubscription creates a test user with an active subscription
func CreateTestUserWithSubscription(t *testing.T, email, name string, creditLimit int) (*models.User, *models.Subscription, string) {
	// Create user
	user, token := CreateTestUserWithAuth(t, email, name)

	// Create subscription tier
	tier, err := CreateTestSubscriptionTier("Test Tier", creditLimit)
	require.NoError(t, err)

	// Create subscription
	subscription, err := CreateTestSubscription(user.ID, tier.ID)
	require.NoError(t, err)

	return user, subscription, token
}

// CreateTestUserWithAPIKey creates a test user with an API key
func CreateTestUserWithAPIKey(t *testing.T, email, name string) (*models.User, *models.APIKey, string) {
	user, token := CreateTestUserWithAuth(t, email, name)

	apiKey, err := CreateTestAPIKey(user.ID, "Test API Key")
	require.NoError(t, err)

	return user, apiKey, token
}

// AssertValidUUID asserts that a string is a valid UUID
func AssertValidUUID(t *testing.T, value interface{}) {
	str, ok := value.(string)
	assert.True(t, ok, "Expected string value for UUID")
	if ok {
		_, err := uuid.Parse(str)
		assert.NoError(t, err, "Expected valid UUID format")
	}
}

// AssertValidTimestamp asserts that a string is a valid timestamp
func AssertValidTimestamp(t *testing.T, value interface{}) {
	str, ok := value.(string)
	assert.True(t, ok, "Expected string value for timestamp")
	if ok {
		assert.True(t, strings.Contains(str, "T"), "Expected ISO timestamp format")
	}
}

// AssertPaginatedResponse asserts that the response contains pagination fields
func AssertPaginatedResponse(t *testing.T, resp *TestResponse) {
	assert.Contains(t, resp.Body, "data")
	assert.Contains(t, resp.Body, "total")
	assert.Contains(t, resp.Body, "page")
	assert.Contains(t, resp.Body, "limit")
}

// AssertArrayResponse asserts that the response contains an array of items
func AssertArrayResponse(t *testing.T, resp *TestResponse, expectedMinLength int) {
	data, exists := resp.Body["data"]
	assert.True(t, exists, "Expected 'data' field in response")

	if exists {
		dataArray, ok := data.([]interface{})
		assert.True(t, ok, "Expected 'data' to be an array")
		if ok {
			assert.GreaterOrEqual(t, len(dataArray), expectedMinLength,
				"Expected at least %d items in response", expectedMinLength)
		}
	}
}

// CleanupTestUser removes a test user and all related data
func CleanupTestUser(t *testing.T, userID uuid.UUID) {
	// Delete in order of dependencies
	TestDB.Where("user_id = ?", userID).Delete(&models.Usage{})
	TestDB.Where("user_id = ?", userID).Delete(&models.Transaction{})
	TestDB.Where("user_id = ?", userID).Delete(&models.APIKey{})
	TestDB.Where("user_id = ?", userID).Delete(&models.Subscription{})
	TestDB.Where("user_id = ?", userID).Delete(&models.Webhook{})
	TestDB.Where("user_id = ?", userID).Delete(&models.AnalyticsData{})
	TestDB.Where("owner_user_id = ?", userID).Delete(&models.MerchantShop{})
	TestDB.Where("owner_id = ?", userID).Delete(&models.Organization{})
	TestDB.Where("id = ?", userID).Delete(&models.User{})
}

// SetupTestData creates a complete test data set
func SetupTestData(t *testing.T) (*models.User, *models.APIKey, *models.Subscription, string) {
	// Create user with subscription
	user, subscription, token := CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)

	// Create API key
	apiKey, err := CreateTestAPIKey(user.ID, "Test API Key")
	require.NoError(t, err)

	return user, apiKey, subscription, token
}
