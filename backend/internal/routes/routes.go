package routes

import (
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/middleware"
	"github.com/gin-gonic/gin"
)

// RegisterRoutes sets up all API routes
func RegisterRoutes(r *gin.Engine) {
	// Register unified shop routes
	RegisterShopRoutes(r)
	// Register merchant routes (legacy - will be deprecated)
	// RegisterMerchantRoutes(r) // Temporarily disabled due to route conflicts
	// Public routes
	public := r.Group("/api/v1")
	{
		// Health check
		public.GET("/health", handlers.HealthCheck)

		// Scheduled tasks (protected by API key)
		public.POST("/tasks/process-scheduled-credits", middleware.ValidateAPIKey(), handlers.ProcessScheduledCredits)
		public.POST("/tasks/process-credit-resets", middleware.ValidateAPIKey(), handlers.ProcessCreditResets)

		// Authentication
		auth := public.Group("/auth")
		{
			auth.POST("/login", handlers.Login)
			auth.POST("/google", handlers.GoogleAuth)
			auth.POST("/refresh", handlers.RefreshToken)
			auth.POST("/credentials", handlers.Login) // Use the same Login handler for credentials
			auth.POST("/register", handlers.RegisterUser)
			auth.POST("/forgot-password", handlers.ForgotPassword)
			auth.POST("/reset-password", handlers.ResetPassword)
		}

		// External API for third-party systems
		external := public.Group("/external")
		{
			external.POST("/verify", handlers.VerifyAPIKey)
			external.POST("/consume", middleware.RateLimitMiddleware(), middleware.ValidateAPIKey(), handlers.ConsumeCredits)
		}
	}

	// Protected routes (require authentication)
	protected := r.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware())
	{
		// User management
		users := protected.Group("/users")
		{
			users.GET("/me", handlers.GetCurrentUser)
			users.PUT("/me", handlers.UpdateUser)
		}

		// Organization management - moved to shop routes for unified system
		// protected.GET("/organizations", handlers.GetOrganizations)
		// protected.POST("/organizations", handlers.CreateOrganization)
		// protected.GET("/organizations/:slug", handlers.GetOrganizationBySlug)
		// protected.PUT("/organizations/:slug", handlers.UpdateOrganization)
		// protected.DELETE("/organizations/:slug", handlers.DeleteOrganization)

		// Legacy organization endpoints (for backward compatibility) - moved to shop routes
		// protected.GET("/organizations/id/:id", handlers.GetOrganization)

		// Branch management - moved to shop routes
		// protected.GET("/org-branches", handlers.GetBranches)
		// protected.POST("/org-branches", handlers.CreateBranch)
		// protected.GET("/org-branches/:id", handlers.GetBranch)
		// protected.PUT("/org-branches/:id", handlers.UpdateBranch)
		// protected.DELETE("/org-branches/:id", handlers.DeleteBranch)

		// External user management - moved to shop routes
		// protected.GET("/org-users", handlers.GetExternalUsers)
		// protected.POST("/org-users", handlers.CreateExternalUser)
		// protected.GET("/org-users/:id", handlers.GetExternalUser)
		// protected.PUT("/org-users/:id", handlers.UpdateExternalUser)
		// protected.DELETE("/org-users/:id", handlers.DeleteExternalUser)
		// protected.POST("/org-users/:id/credits/add", handlers.AddCreditsToExternalUser)
		// protected.POST("/org-users/:id/credits/reduce", handlers.ReduceCreditsFromExternalUser)

		// API key management
		apiKeys := protected.Group("/apikeys")
		{
			apiKeys.GET("", handlers.GetAPIKeys)
			apiKeys.POST("", handlers.CreateAPIKey)
			apiKeys.GET("/:id", handlers.GetAPIKey)
			apiKeys.PUT("/:id", handlers.UpdateAPIKey)
			apiKeys.DELETE("/:id", handlers.DeleteAPIKey)
		}

		// Credit management
		credits := protected.Group("/credits")
		{
			credits.GET("", handlers.GetCreditBalance)
			credits.POST("/add", handlers.AddCredits)
			credits.GET("/transactions", handlers.GetTransactions)
			credits.GET("/scheduled/next", handlers.GetNextScheduledCreditDate)
			credits.GET("/scheduled/history", handlers.GetScheduledCreditHistory)
		}

		// Usage statistics
		usage := protected.Group("/usage")
		{
			usage.GET("", handlers.GetUsage)
			usage.GET("/summary", handlers.GetUsageSummary)
		}

		// Subscription management
		subscriptions := protected.Group("/subscriptions")
		{
			subscriptions.GET("", handlers.GetSubscriptions)
			subscriptions.GET("/active", handlers.GetActiveSubscription)
			subscriptions.GET("/tiers", handlers.GetSubscriptionTiers)
			subscriptions.POST("", handlers.CreateSubscription)
			subscriptions.PUT("/:id", handlers.UpdateSubscription)
			subscriptions.DELETE("/:id", handlers.CancelSubscription)
			subscriptions.POST("/:id/upgrade", handlers.UpgradeSubscription)
		}

		// Stripe integration
		stripe := protected.Group("/stripe")
		{
			stripe.POST("/create-checkout-session", handlers.CreateCheckoutSession)
		}

		// Stripe webhooks (no authentication required)
		r.POST("/webhook/stripe", handlers.HandleStripeWebhook)

		// API endpoint for frontend to create checkout session
		r.POST("/api/stripe/create-checkout-session", handlers.CreateCheckoutSession)

		// Webhook management
		webhooks := protected.Group("/webhooks")
		{
			webhooks.GET("", handlers.GetWebhooks)
			webhooks.POST("", handlers.CreateWebhook)
			webhooks.GET("/:id", handlers.GetWebhook)
			webhooks.PUT("/:id", handlers.UpdateWebhook)
			webhooks.DELETE("/:id", handlers.DeleteWebhook)
			webhooks.GET("/:id/deliveries", handlers.GetWebhookDeliveries)
		}

		// Advanced analytics
		analytics := protected.Group("/analytics")
		{
			analytics.GET("/summary", handlers.GetAnalyticsSummary)
			analytics.GET("/trends", handlers.GetAnalyticsTrends)
			analytics.GET("/endpoints", handlers.GetEndpointAnalytics)
			analytics.GET("/performance", handlers.GetPerformanceMetrics)
		}
	}

	// Admin routes (require admin role)
	admin := r.Group("/api/v1/admin")
	admin.Use(middleware.AuthMiddleware(), middleware.AdminMiddleware())
	{
		admin.GET("/users", handlers.GetAllUsers)
		admin.GET("/users/:id", handlers.GetUser)
		admin.PUT("/users/:id", handlers.UpdateUserByAdmin)
		admin.DELETE("/users/:id", handlers.DeleteUser)

		admin.GET("/subscriptions", handlers.GetAllSubscriptions)
		admin.POST("/subscription-tiers", handlers.CreateSubscriptionTier)
		admin.PUT("/subscription-tiers/:id", handlers.UpdateSubscriptionTier)
		admin.DELETE("/subscription-tiers/:id", handlers.DeleteSubscriptionTier)

		// Scheduled credits admin endpoints
		admin.POST("/credits/scheduled/process", handlers.ProcessScheduledCredits)
		admin.POST("/credits/scheduled/manual", handlers.ManuallyAddScheduledCredits)
	}
}
