package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID                     uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Email                  string         `gorm:"uniqueIndex;not null" json:"email"`
	Name                   string         `json:"name"`
	Picture                string         `json:"picture"`
	Password               string         `json:"-"` // Password is never returned in JSON
	GoogleID               string         `gorm:"uniqueIndex" json:"google_id,omitempty"`
	Role                   string         `gorm:"default:user" json:"role"`
	ResetToken             string         `json:"-"` // Password reset token
	ResetTokenExpiresAt    *time.Time     `json:"-"` // Password reset token expiration
	OrganizationID         *uuid.UUID     `gorm:"type:uuid" json:"organization_id"`
	BranchID               *uuid.UUID     `gorm:"type:uuid" json:"branch_id"`
	ShopID                 *uuid.UUID     `gorm:"type:uuid" json:"shop_id"`
	ShopBranchID           *uuid.UUID     `gorm:"type:uuid" json:"shop_branch_id"`
	MonthlyCredits         int            `gorm:"default:0" json:"monthly_credits"`
	NextCreditResetDate    *time.Time     `json:"next_credit_reset_date"`
	IsExternalUser         bool           `gorm:"default:false" json:"is_external_user"` // Indicates if user was created by an external service
	ExternalUserIdentifier string         `json:"external_user_identifier,omitempty"`    // External identifier for users created by external services
	APIKeys                []APIKey       `json:"api_keys,omitempty"`
	Subscriptions          []Subscription `json:"subscriptions,omitempty"`
	Transactions           []Transaction  `json:"transactions,omitempty"`
	Webhooks               []Webhook      `json:"webhooks,omitempty"`
	CreatedAt              time.Time      `json:"created_at"`
	UpdatedAt              time.Time      `json:"updated_at"`
	DeletedAt              gorm.DeletedAt `gorm:"index" json:"-"`
}

// APIKey represents an API key for accessing the credit system
type APIKey struct {
	ID            uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID        uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	ShopID        *uuid.UUID     `gorm:"type:uuid" json:"shop_id"`        // Optional: Shop-level API key
	ShopBranchID  *uuid.UUID     `gorm:"type:uuid" json:"shop_branch_id"` // Optional: Branch-level API key
	Name          string         `json:"name"`
	Key           string         `gorm:"uniqueIndex;not null" json:"key"`
	LastUsed      *time.Time     `json:"last_used"`
	Enabled       bool           `gorm:"default:true" json:"enabled"`
	Permissions   StringSlice    `gorm:"type:text" json:"permissions"`
	RateLimitMax  int            `gorm:"default:60" json:"rate_limit_max"`
	RateLimitRate float64        `gorm:"default:1.0" json:"rate_limit_rate"`
	Usage         []Usage        `json:"usage,omitempty"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`
}

// SubscriptionTier represents a subscription tier with specific limits
type SubscriptionTier struct {
	ID                   uint           `gorm:"primaryKey" json:"id"`
	Name                 string         `json:"name"`
	Description          string         `json:"description"`
	Price                float64        `json:"price"`
	CreditLimit          int            `json:"credit_limit"`
	Features             StringSlice    `gorm:"type:text" json:"features"`
	DefaultRateLimitMax  int            `gorm:"default:60" json:"default_rate_limit_max"`
	DefaultRateLimitRate float64        `gorm:"default:1.0" json:"default_rate_limit_rate"`
	MaxWebhooks          int            `gorm:"default:1" json:"max_webhooks"`
	AdvancedAnalytics    bool           `gorm:"default:false" json:"advanced_analytics"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	DeletedAt            gorm.DeletedAt `gorm:"index" json:"-"`
}

// Subscription represents a user's subscription to a specific tier
type Subscription struct {
	ID                 uuid.UUID        `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID             uuid.UUID        `gorm:"type:uuid;not null" json:"user_id"`
	SubscriptionTierID uint             `json:"subscription_tier_id"`
	SubscriptionTier   SubscriptionTier `json:"subscription_tier"`
	StartDate          time.Time        `json:"start_date"`
	EndDate            *time.Time       `json:"end_date"`
	AutoRenew          bool             `gorm:"default:true" json:"auto_renew"`
	Status             string           `gorm:"default:active" json:"status"`
	CreditBalance      int              `gorm:"default:0" json:"credit_balance"`
	SubscriptionType   string           `gorm:"default:personal" json:"subscription_type"` // 'personal', 'merchant', or 'customer'
	MerchantShopID     *uuid.UUID       `gorm:"type:uuid" json:"merchant_shop_id,omitempty"`
	MerchantShop       *MerchantShop    `json:"merchant_shop,omitempty" gorm:"foreignKey:MerchantShopID"`
	ShopID             *uuid.UUID       `gorm:"type:uuid" json:"shop_id,omitempty"`
	// Shop                 *Shop            `json:"shop,omitempty" gorm:"foreignKey:ShopID"` // Temporarily disabled
	ShopCustomerID       *uuid.UUID     `gorm:"type:uuid" json:"shop_customer_id,omitempty"`
	ShopCustomer         *ShopCustomer  `json:"shop_customer,omitempty" gorm:"foreignKey:ShopCustomerID"`
	StripeSubscriptionID string         `json:"stripe_subscription_id,omitempty"` // Stripe subscription ID for tracking
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	DeletedAt            gorm.DeletedAt `gorm:"index" json:"-"`
}

// Usage represents API usage by an API key
type Usage struct {
	ID           uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	APIKeyID     uuid.UUID      `gorm:"type:uuid;not null" json:"api_key_id"`
	Endpoint     string         `json:"endpoint"`
	Method       string         `json:"method"`
	Credits      int            `json:"credits"`
	Timestamp    time.Time      `json:"timestamp"`
	Success      bool           `gorm:"default:true" json:"success"`
	IPAddress    string         `json:"ip_address"`
	UserAgent    string         `json:"user_agent"`
	ResponseTime int            `json:"response_time"` // in milliseconds
	StatusCode   int            `json:"status_code"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// Transaction represents a credit transaction (addition or consumption)
type Transaction struct {
	ID             uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID         uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	SubscriptionID uuid.UUID      `gorm:"type:uuid" json:"subscription_id"`
	OrganizationID *uuid.UUID     `gorm:"type:uuid" json:"organization_id"`
	BranchID       *uuid.UUID     `gorm:"type:uuid" json:"branch_id"`
	Type           string         `json:"type"` // "credit_add", "credit_use", "credit_scheduled", "credit_reset"
	Amount         int            `json:"amount"`
	Description    string         `json:"description"`
	Reference      string         `json:"reference"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"-"`
}

// Webhook represents a webhook configuration for event notifications
type Webhook struct {
	ID         uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID     uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	Name       string         `json:"name"`
	URL        string         `json:"url"`
	Secret     string         `json:"secret"`
	Events     StringSlice    `gorm:"type:text[]" json:"events"` // "credit.consumed", "credit.added", "api_key.created", etc.
	Active     bool           `gorm:"default:true" json:"active"`
	LastCalled *time.Time     `json:"last_called"`
	FailCount  int            `gorm:"default:0" json:"fail_count"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// WebhookDelivery represents a record of a webhook delivery attempt
type WebhookDelivery struct {
	ID         uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	WebhookID  uuid.UUID      `gorm:"type:uuid;not null" json:"webhook_id"`
	Event      string         `json:"event"`
	Payload    string         `gorm:"type:text" json:"payload"`
	StatusCode int            `json:"status_code"`
	Response   string         `gorm:"type:text" json:"response"`
	Success    bool           `gorm:"default:false" json:"success"`
	Duration   int            `json:"duration"` // in milliseconds
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// AnalyticsData represents aggregated analytics data for advanced reporting
type AnalyticsData struct {
	ID              uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID          uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	Date            time.Time      `json:"date"`
	APIKeyID        *uuid.UUID     `json:"api_key_id"`
	Endpoint        string         `json:"endpoint"`
	TotalRequests   int            `json:"total_requests"`
	TotalCredits    int            `json:"total_credits"`
	AvgResponseTime int            `json:"avg_response_time"` // in milliseconds
	ErrorRate       float64        `json:"error_rate"`        // percentage
	P95ResponseTime int            `json:"p95_response_time"` // 95th percentile response time
	P99ResponseTime int            `json:"p99_response_time"` // 99th percentile response time
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`
}
