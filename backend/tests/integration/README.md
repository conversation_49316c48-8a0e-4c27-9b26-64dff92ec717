# Subscription Guard Integration Tests

This directory contains comprehensive integration tests for the subscription guard system that enforces subscription limits across the ADC Credit platform.

## 🎯 Overview

The subscription guard system ensures that users can only perform actions within their subscription tier limits. These tests verify that all limits are properly enforced at the API level.

## 📁 Test Files

### Core Test Files

- **`subscription_guard_test.go`** - Main subscription guard functionality tests
- **`subscription_limits_api_test.go`** - API endpoint tests for subscription limits
- **`qr_code_limits_test.go`** - Specialized QR code monthly limit tests

### Test Utilities

- **`run_integration_tests.go`** - Test runner with coverage reporting
- **`../internal/testing/setup.go`** - Test database setup and utilities

## 🧪 Test Categories

### 1. Shop Creation Limits
Tests that users cannot create more shops than their subscription allows.

**Scenarios:**
- ✅ Starter plan: 1 shop limit
- ✅ Business plan: 5 shop limit  
- ✅ Enterprise plan: Unlimited shops

### 2. Customer Limits per Shop
Tests customer addition limits for each shop.

**Scenarios:**
- ✅ Starter plan: 50 customers per shop
- ✅ Business plan: 1,000 customers per shop
- ✅ Enterprise plan: Unlimited customers

### 3. API Key Limits per Shop
Tests API key creation limits for each shop.

**Scenarios:**
- ✅ Starter plan: 1 API key per shop
- ✅ Business plan: 5 API keys per shop
- ✅ Enterprise plan: 20 API keys per shop

### 4. Branch Limits per Shop
Tests branch creation limits for each shop.

**Scenarios:**
- ✅ Starter plan: 0 branches (not allowed)
- ✅ Business plan: 10 branches per shop
- ✅ Enterprise plan: Unlimited branches

### 5. QR Code Monthly Limits
Tests QR code generation limits on a monthly basis.

**Scenarios:**
- ✅ Starter plan: 500 QR codes per month
- ✅ Business plan: 2,000 QR codes per month
- ✅ Enterprise plan: Unlimited QR codes
- ✅ Monthly reset functionality

### 6. Webhook Limits
Tests webhook creation limits.

**Scenarios:**
- ✅ Starter plan: 0 webhooks (not allowed)
- ✅ Business plan: 3 webhooks
- ✅ Enterprise plan: 15 webhooks

### 7. Shop Type Restrictions
Tests that users can only create allowed shop types.

**Scenarios:**
- ✅ Starter plan: Only "retail" shops
- ✅ Business plan: "retail" and "api_service" shops
- ✅ Enterprise plan: All shop types

### 8. API Endpoint Tests
Tests all subscription limit API endpoints.

**Endpoints:**
- ✅ `GET /api/v1/subscription-limits` - Get all user limits
- ✅ `GET /api/v1/subscription-limits/shops/check` - Check shop limits
- ✅ `GET /api/v1/subscription-limits/qr-codes/check` - Check QR code limits
- ✅ `GET /api/v1/subscription-limits/webhooks/check` - Check webhook limits
- ✅ `GET /api/v1/subscription-limits/shop-types/check` - Check shop type permissions

## 🚀 Running Tests

### Prerequisites

1. **PostgreSQL Database** - Running locally or accessible
2. **Go 1.21+** - For running backend tests
3. **Environment Variables** - Properly configured

### Quick Start

```bash
# Run all subscription guard tests
make test-subscription-guard

# Run specific test categories
make test-shop-limits
make test-qr-code-limits
make test-webhook-limits

# Generate coverage report
make test-subscription-coverage
```

### Manual Test Execution

```bash
# Setup test database
make setup-test-db

# Seed subscription tiers
make seed-subscription-tiers

# Run individual test files
cd backend
go test -v ./tests/integration/subscription_guard_test.go
go test -v ./tests/integration/subscription_limits_api_test.go
go test -v ./tests/integration/qr_code_limits_test.go

# Run with coverage
go test -coverprofile=coverage.out ./tests/integration/...
go tool cover -html=coverage.out -o coverage.html
```

### Test Runner Script

```bash
# Use the custom test runner
cd backend
go run tests/run_integration_tests.go
```

## 📊 Test Data

### Subscription Tiers Used in Tests

| Tier | Price | Shops | Customers/Shop | API Keys/Shop | QR Codes/Month | Webhooks |
|------|-------|-------|----------------|---------------|----------------|----------|
| **Starter** | $0 | 1 | 50 | 1 | 500 | 0 |
| **Business** | $49 | 5 | 1,000 | 5 | 2,000 | 3 |
| **Enterprise** | $199 | ∞ | ∞ | 20 | ∞ | 15 |

### Test Users

Each test creates isolated users with specific subscription tiers to avoid conflicts.

## 🔧 Configuration

### Environment Variables

```bash
# Test Database
DB_NAME=adc_credit_test
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password

# Test Mode
GIN_MODE=test
```

### Test Database Setup

The tests automatically:
1. Create a test database
2. Run migrations
3. Seed subscription tiers
4. Clean up after each test

## 📈 Coverage Goals

- **Overall Coverage**: >90%
- **Subscription Guard Service**: >95%
- **API Endpoints**: >90%
- **Middleware**: >85%

## 🐛 Debugging Tests

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check PostgreSQL is running
   pg_isready -h localhost -p 5432
   
   # Verify test database exists
   psql -h localhost -U postgres -l | grep adc_credit_test
   ```

2. **Test Data Conflicts**
   ```bash
   # Clean test database
   make clean-test-db
   make setup-test-db
   ```

3. **Environment Variable Issues**
   ```bash
   # Check test environment file
   cat backend/.env.test
   ```

### Verbose Test Output

```bash
# Run with verbose output
go test -v -run TestSpecificTest ./tests/integration/...

# Run single test method
go test -v -run TestShopCreationLimits ./tests/integration/subscription_guard_test.go
```

## 🔄 Continuous Integration

### GitHub Actions Integration

```yaml
name: Integration Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: 1.21
      - name: Run Integration Tests
        run: make test-subscription-guard
```

## 📝 Adding New Tests

### Test Structure

```go
func (suite *TestSuite) TestNewFeature() {
    // 1. Setup test data
    tier := suite.createTestSubscriptionTier(...)
    user, _, token := suite.createUserWithSubscription(...)
    
    // 2. Perform action that should be limited
    // ... make API request
    
    // 3. Assert expected behavior
    suite.Equal(expectedStatusCode, actualStatusCode)
    suite.Contains(response, expectedMessage)
    
    // 4. Cleanup
    testutils.CleanupTestUser(suite.T(), user.ID)
}
```

### Best Practices

1. **Isolation** - Each test should be independent
2. **Cleanup** - Always clean up test data
3. **Descriptive Names** - Test names should describe the scenario
4. **Edge Cases** - Test boundary conditions
5. **Error Messages** - Verify user-friendly error messages

## 🎯 Future Enhancements

- [ ] Performance tests for high-volume scenarios
- [ ] Concurrent access tests
- [ ] Rate limiting integration tests
- [ ] Subscription upgrade/downgrade tests
- [ ] Credit balance enforcement tests
- [ ] Analytics access control tests

## 📞 Support

For questions about the integration tests:

1. Check the test output for specific error messages
2. Review the test database setup
3. Verify environment configuration
4. Check the main README for general setup instructions
